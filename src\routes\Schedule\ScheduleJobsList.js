import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Card, Table, Modal, Divider, Popconfirm } from 'antd';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';

// 使用 dva 的 connect 装饰器连接 model 和 loading 状态
@connect(({ gm, loading }) => ({
  gm,
  loading: loading.models.gm, // 连接 gm model 的 loading 状态
}))
export default class ScheduleJobsList extends PureComponent {
  // 初始化组件状态
  state = {
    visible: false, // 控制删除确认弹窗的显示
    selectedRecordId: null, // 存储当前选中要删除的记录ID
  };

  // 组件加载后，立即获取定时任务列表
  componentDidMount() {
    this.props.dispatch({
      type: 'gm/fetchScheduleJobs',
    });
  }

  // 确认删除
  handleOk = () => {
    this.props.dispatch({
      type: 'gm/deleteScheduleJob',
      jobId: this.state.selectedRecordId,
    });
    this.setState({
      visible: false,
      selectedRecordId: null,
    });
  };

  // 取消删除
  handleCancel = () => {
    this.setState({
      visible: false,
      selectedRecordId: null,
    });
  };

  // 点击删除按钮时，记录ID并弹出确认框
  handleSelectedRecord = (id) => {
    this.setState({
      visible: true,
      selectedRecordId: id,
    });
  };

  render() {
    // 定义表格的列
    const columns = [
      {
        title: 'GM指令名称',
        dataIndex: 'commandName',
      },
      {
        title: '运行服务器ID',
        dataIndex: 'serverIds',
        // 如果 serverIds 是数组，则用逗号连接显示
        render: val => (Array.isArray(val) ? val.join(', ') : val),
      },
      {
        title: 'GM指令参数',
        dataIndex: 'params',
        // 将参数数组转换成一个字符串显示
        render: val => (Array.isArray(val) ? val.join(' ') : ''),
      },
      {
        title: '执行时间',
        dataIndex: 'triggerTime',
        // 使用 moment.js 格式化时间戳
        render: val => <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '创建者',
        dataIndex: 'userName',
      },
      {
        title: '操作',
        render: (text, record) => (
          <Fragment>
            <a>详情</a>
            <Divider type="vertical" />
            <a onClick={() => this.handleSelectedRecord(record.keyName)}>删除</a>
          </Fragment>
        ),
      },
    ];

    const { gm: { scheduleJobs }, loading } = this.props;

    return (
      // 注意：代码中使用了 "角色批量发奖记录" 作为标题，您可能需要根据实际业务修改
      <PageHeaderLayout title="定时任务记录">
        <Card bordered={false}>
          <div>
            <Table
              rowKey="id"
              dataSource={scheduleJobs}
              columns={columns}
              pagination={false} // 根据您的代码，这里没有分页
              loading={loading}
            />
          </div>
        </Card>
        <Modal
          title="删除确认"
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <p>确认删除</p>
        </Modal>
      </PageHeaderLayout>
    );
  }
}