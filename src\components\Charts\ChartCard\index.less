@import "~antd/lib/style/themes/default.less";

.chartCard {
  position: relative;
  .chartTop {
    position: relative;
    overflow: hidden;
    width: 100%;
  }
  .chartTopMargin {
    margin-bottom: 12px;
  }
  .chartTopHasMargin {
    margin-bottom: 20px;
  }
  .metaWrap {
    float: left;
  }
  .avatar {
    position: relative;
    top: 4px;
    float: left;
    margin-right: 20px;
    img {
      border-radius: 100%;
    }
  }
  .meta {
    color: @text-color-secondary;
    font-size: @font-size-base;
    line-height: 22px;
    height: 22px;
  }
  .action {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
  }
  .total {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
    color: @heading-color;
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 30px;
    line-height: 38px;
    height: 38px;
  }
  .content {
    margin-bottom: 12px;
    position: relative;
    width: 100%;
  }
  .contentFixed {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
  }
  .footer {
    border-top: 1px solid @border-color-split;
    padding-top: 9px;
    margin-top: 8px;
    & > * {
      position: relative;
    }
  }
  .footerMargin {
    margin-top: 20px;
  }
}

.spin :global(.ant-spin-container) {
  overflow: visible;
}
