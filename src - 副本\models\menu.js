import { queryMenu, createMenu, updateMenu } from '../services/menu';

export default {
  namespace: 'menu',

  state: { menus: [] },

  effects: {
    * fetchMenu(payload, { call, put }) {
      const response = yield call(queryMenu);
      yield put({
        type: 'saveMenu',
        payload: response,
      });
    },
    * createMenu({ payload }, { call, put }) {
      const response = yield call(createMenu, payload);
      yield put({
        type: 'saveMenu',
        payload: response,
      });
    },
    * updateMenu({ menuId, payload }, { call, put }) {
      const response = yield call(updateMenu, menuId, payload);
      yield put({
        type: 'saveMenu',
        payload: response,
      });
    },
  },

  reducers: {
    saveMenu(state, action) {
      return {
        ...state,
        menus: action.payload,
      };
    },
  },
};
