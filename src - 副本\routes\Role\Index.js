import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Card, Form, Input, Modal, Divider } from 'antd';
import { Link } from 'dva/router';
import StandardTable from '../../components/StandardTable/index';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';


const FormItem = Form.Item;

const CreateForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item } = props;
  const okHandle = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      handleOk(fieldsValue);
    });
  };
  return (
    <Modal
      title={title}
      visible={modalVisible}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="角色名称"
      >
        {form.getFieldDecorator('roleName', {
          rules: [{ required: true, message: '请输入角色名称' }],
        })(
          <Input placeholder="请输入角色名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="角色描述"
      >
        {form.getFieldDecorator('roleDesc', {
          rules: [{ required: true, message: '请输入角色描述' }],
        })(
          <Input placeholder="请输入角色描述"/>
        )}
      </FormItem>
    </Modal>
  );
});

@connect(({ role }) => ({
  role,
}))
@Form.create()
export default class TableList extends PureComponent {
  state = {
    modalVisible: false,
    expandForm: false,
    selectedRows: [],
    formValues: {},
    modal: {
      modalVisible: false,
    }
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'role/fetchRole',
    });
  }

  handleCreateSubmit = (data) => {
    this.props.dispatch({
      type: 'role/createRole',
      payload: { ...data },
    }).then(this.setState({
          modal: {
            modalVisible: false,
          }
        })
      );
  };
  handleModalCancel = () => {
    this.setState({
      modal: {
        modalVisible: false,
      }
    });
  };

  handleDeleteUser = userId => {
    this.props.dispatch({
      type: 'role/deleteRole',
      roleId: userId
    })
  };

  handleSelectRows = (rows) => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleModalVisible = () => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data) => this.handleCreateSubmit(data),
        handleCancel: this.handleModalCancel,
      }
    });
  };


  render() {
    const { role: { roles }, } = this.props;
    const { selectedRows, modal } = this.state;
    const columns = [
      {
        title: '角色',
        dataIndex: 'roleName',
      },
      {
        title: '角色描述',
        dataIndex: 'roleDesc',
      },
      {
        title: '操作',
        dataIndex: 'id',
        render: roleId => <Fragment>
          <a onClick={() => this.handleDeleteUser(roleId)}>删除</a>
          <Divider type="vertical" />
          <Link to={"/roles/"+roleId}>编辑权限</Link>
        </Fragment>
      },
    ];

    return (
      <PageHeaderLayout title="查询表格">
        <Card bordered={false}>advanced
          <div>
            <div>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible(true)}>
                新建
              </Button>
            </div>
            <StandardTable
              selectedRows={selectedRows}
              data={{ list: roles }}
              columns={columns}
              onSelectRow={this.handleSelectRows}
            />
          </div>
        </Card>
        <CreateForm {...modal}/>
      </PageHeaderLayout>
    );
  }
}
