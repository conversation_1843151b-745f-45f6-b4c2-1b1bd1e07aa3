import request from '../utils/request';

export async function query() {
  return request('/api/users');
}

export async function queryCurrent() {
  return request('/currentUser');
}

export async function queryMenu() {
  return request('/menus');
}

export async function queryUsers() {
  return request('/api/users');
}

export async function createUser(params) {
  return request('/users', {
    method: 'POST',
    body: params,
  });
}

export async function deleteUser(userId) {
  return request('/api/users/' + userId, {
    method: 'DELETE',
  });
}


