import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Card, Form, Input, Modal, Select } from 'antd';
import StandardTable from '../../components/StandardTable/index';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';


const FormItem = Form.Item;
const { Option } = Select;
const getValue = obj => Object.keys(obj)
  .map(key => obj[key])
  .join(',');
const statusMap = ['default', 'processing', 'success', 'error'];
const status = ['关闭', '运行中', '已上线', '异常'];

const CreateForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, roles } = props;
  const okHandle = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      handleOk(fieldsValue);
    });
  };

  const serverItems = roles.map((role, index) =>
    <Select.Option key={index} value={role.id}>
      {role.roleDesc}
    </Select.Option>);
  return (
    <Modal
      title={title}
      visible={modalVisible}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="用户名"
      >
        {form.getFieldDecorator('userName', {
          rules: [{ required: true, message: '请输入新建用户名' }],
        })(
          <Input placeholder="请输入新建用户名"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="密码"
      >
        {form.getFieldDecorator('password', {
          rules: [{ required: true, message: '请输入密码' }],
        })(
          <Input placeholder="请输入密码"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 50 }}
        label="角色"
      >
        {form.getFieldDecorator('roleId', {
          rules: [{ required: true, message: '请选择角色' }],
        })(
          <Select allowClear={true} style={{ maxWidth: 286, width: '100%' }}
                  placeholder="请选择角色"
                  filterOption={(input, option) => option.props.value.toString()
                    .indexOf(input) === 0}
          >
            {serverItems}
          </Select>
        )}
      </FormItem>
    </Modal>
  );
});

@connect(({ user, role, loading }) => ({
  user,
  role,
  loading: loading.models.rule,
}))
@Form.create()
export default class TableList extends PureComponent {
  state = {
    modalVisible: false,
    expandForm: false,
    selectedRows: [],
    formValues: {},
    modal: {
      modalVisible: false,
    }
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'role/fetchRole',
    });
    dispatch({
      type: 'user/fetchUsers',
    });
  }

  handleCreateSubmit = (data) => {
    this.props.dispatch({
      type: 'user/createUser',
      payload: { ...data },

    })
      .then(this.setState({
        modal: {
          modalVisible: false,
        }
      }))
      .then(this.props.dispatch({
        type: 'user/fetchUsers',
      }));
  };
  handleModalCancel = () => {
    this.setState({
      modal: {
        modalVisible: false,
      }
    });
  };

  handleDeleteUser = userId => {
    this.props.dispatch({
      type: 'user/deleteUser',
      userId: userId
    })
      .then(this.props.dispatch({
        type: 'user/fetchUsers',
      }));
  };

  handleSelectRows = (rows) => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleModalVisible = () => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data) => this.handleCreateSubmit(data),
        handleCancel: this.handleModalCancel,
      }
    });
  };


  render() {
    const { user: { users }, role: { roles, roleMap }, loading } = this.props;
    const { selectedRows, modal } = this.state;
    const columns = [
      {
        title: '用户',
        dataIndex: 'name',
      },
      {
        title: '角色',
        dataIndex: 'role.roleDesc',
      },
      {
        title: '最近登入时间',
        dataIndex: 'lastLoginTime',
        sorter: true,
        // render: val => <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '操作',
        dataIndex: 'id',
        render: userId => <Fragment>
          <a onClick={() => this.handleDeleteUser(userId)}>删除</a>
        </Fragment>
      },
    ];

    return (
      <PageHeaderLayout title="查询表格">
        <Card bordered={false}>advanced
          <div>
            <div>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible(true)}>
                新建
              </Button>
            </div>
            <StandardTable
              selectedRows={selectedRows}
              // loading={loading}
              data={{ list: users }}
              columns={columns}
              onSelectRow={this.handleSelectRows}
            />
          </div>
        </Card>
        <CreateForm {...modal} roles={roles}/>
      </PageHeaderLayout>
    );
  }
}
