import React, { Component } from 'react';
import { connect } from 'dva';
import { Button, Checkbox, Card, Form, Select, Tree, Switch } from 'antd';


@connect(({ role, permission }) => ({
  role,
  permission,
}))
@Form.create()
export default class Edit extends Component {
  state = {
    role: null,
    rolePermissions: new Map()
  };

  onChange = (permission, checked) => {
    this.props.dispatch({
      type: 'role/updateRolePermissions',
      payload: {
        checked: checked,
        permissionId: permission.id,
      },
      roleId: this.props.match.params.roleId
    }).then(this.updateRolePermission);
  };

  updateRolePermission = (data)=>{
    const map = new Map();
    data._embedded.sysPermissions.forEach(p => {
      var s = p._links.self.href.split('/');
      p.id = s[s.length - 1];
      map.set(p.id, p);
    });
    this.setState({ rolePermissions: map});
  }

  componentDidMount() {
    this.props.dispatch({
      type: 'role/getRolePermissions',
      roleId: this.props.match.params.roleId,
    })
      .then(this.updateRolePermission);

    this.props.dispatch({
      type: 'permission/fetchPermission',
    });
    this.props.dispatch({
      type: 'role/getRole',
      roleId: this.props.match.params.roleId,
    }).then(data=>{
      this.setState({ role: data});
    });
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.match.params.roleId && this.props.match.params.roleId !== nextProps.match.params.roleId) {
      this.props.dispatch({
        type: 'role/getRolePermissions',
        roleId: nextProps.match.params.roleId,
      })
        .then(this.updateRolePermission);
    }
  }

  render() {
    const { permission: { permissions } } = this.props;
    const { rolePermissions, role} = this.state;
    const items = permissions.map((permission, index)=>
      <div>{permission.name} key={index}
        <Switch checked={rolePermissions.has(permission.id)} onChange={checked=>this.onChange(permission, checked)}/>
      </div>
    );
    return (
      <>
        <Card title={(role && role.roleDesc || "")+"角色权限编辑（Admin 不需要修改）"}>
        {items}
        </Card>
      </>
    );
  }
}
