import { isUrl } from '../utils/utils';

export const gmRouter = { router: [] };

function formatter(data, parentPath = '/', parentAuthority) {
  return data.map((item) => {
    let { path } = item;
    if (!isUrl(path)) {
      path = parentPath + item.path;
    }
    const result = {
      ...item,
      path,
      authority: item.authority || parentAuthority,
    };
    if (item.children) {
      result.children = formatter(item.children, `${parentPath}${item.path}/`, item.authority);
    }
    return result;
  });
}

export const getMenuData = (menuData) => {
  menuData[0].children = gmRouter.router;
  var m = formatter(menuData)
  return m
};
