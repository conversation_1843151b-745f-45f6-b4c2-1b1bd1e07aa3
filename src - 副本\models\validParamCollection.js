import {query, createValidParamCollection, updateValidParamCollection, deleteValidParamCollection} from '../services/ValidParamCollection';

export default {
  namespace: 'validParamCollection',

  state: { validParamCollections: [] },

  effects: {
    * fetchValidParamCollections(payload, { call, put }) {
      const response = yield call(query);
      const validParamCollections = response._embedded.validParamCollections.map(validParamCollection=>{
        let s = validParamCollection._links.self.href.split('/');
        validParamCollection.id = s[s.length - 1];
        return validParamCollection;
      });
      yield put({
        type: 'saveRole',
        payload: validParamCollections,
      });
    },
    * createValidParamCollection({ payload }, { call, put }) {
      yield call(createValidParamCollection, payload);
      const response = yield put({ type: 'fetchValidParamCollections'});
      return response
    },
    * updateValidParamCollection({ validParamCollectionId, payload }, { call, put }) {
      yield call(updateValidParamCollection, validParamCollectionId, payload);
      const response = yield put({ type: 'fetchValidParamCollections'});
      return response
    },

    * deleteValidParamCollection({ validParamCollectionId, payload }, { call, put }) {
      yield call(deleteValidParamCollection, validParamCollectionId);
      const response = yield put({ type: 'fetchValidParamCollections'});
      return response
    },
  },

  reducers: {
    saveRole(state, action) {
      return {
        ...state,
        validParamCollections: action.payload,
      };
    },
  },
};
