import request from '../utils/request';
import { stringify } from 'qs';

export async function queryModules() {
  return request('/gm/modules');
}

export async function queryServers(wgsName) {
  return request('/gm/servers/' + wgsName);
}

export async function doCommand(commandName, params) {
  return request('/commands/' + commandName, {
    method: 'POST',
    body: params,
  });
}

export async function doCommandByName(params) {
  return request('/commands/callCommand', {
    method: 'POST',
    body: params,
  });
}


export async function getCommand(commandId) {
  return request('/commands/' + commandId, {
    method: 'GET',
  });
}

export async function patchCommand(commandId, params) {
  return request('/commands/' + commandId, {
    method: 'PATCH',
    body: params,
  });
}

export async function doPlayerOnlineGift(params) {
  return request('/commands/PlayerOnlineGift', {
    method: 'POST',
    body: params,
  });
}

export async function queryPlayerOnlineGiftRecord(params) {
  return request(`/commands/PlayerOnlineGift?${stringify(params)}`);
}

export async function deletePlayerOnlineGift(playerOnlineGiftId) {
  return request('/commands/PlayerOnlineGift/' + playerOnlineGiftId, {
    method: 'DELETE',
  });
}

export async function updateCommands(serverId) {
  return request('/gm/servers/' + serverId + '/updateCommands', {
    method: 'GET',
  });
}


// ------------------- 新增的函数 -------------------
/**
 * 发起一个定时命令任务
 * @param params
 * @returns {Promise<*>}
 */
export async function doCommandSchedule(params) {
  // 注意：这里的 /commands/schedule 是一个示例地址，
  // 您需要根据您后端API的实际地址进行修改。
  return request('/commands/schedulejobs', {
    method: 'POST',
    body: params,
  });
}
// ----------------------------------------------------

// 获取定时任务列表
export async function queryScheduleJobs() {
  return request('/commands/schedulejobs'); // 请替换为实际API地址
}

// 删除一个定时任务
export async function deleteScheduleJob(jobId) {
  return request(`/commands/schedulejobs/${jobId}`, { // 请替换为实际API地址
    method: 'DELETE',
  });
}