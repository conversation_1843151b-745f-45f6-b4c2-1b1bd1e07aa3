import React, { Component } from 'react';
import { <PERSON><PERSON>, Card, Col, Form, Input, Row, Select } from 'antd';
import { connect } from 'dva';

const FormItem = Form.Item;

@connect(({ role, paramFilter }) => ({
  role, paramFilter
}))
@Form.create()
class GMForm extends Component {
  state = {
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        console.log(values)
        this.props.dispatch({
          type: 'paramFilter/addParamFilter',
          payload: values,
        });
      }
    });

  };

  componentDidMount() {
    this.props.dispatch({
      type: 'role/fetchRole',
    });

    this.props.dispatch({
      type: 'paramFilter/fetchParamFilter',
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { role: { roles }, paramFilter: { paramFilters } } = this.props;

    const roleItems = roles.map((role, index) =>
      <Select.Option key={index} value={role.id}>
        {role.roleDesc}
      </Select.Option>);

    const filterItems = paramFilters.map((filter, index) =>
      <Select.Option key={index} value={filter.id}>
        {filter.name}
      </Select.Option>);


    return (
      <>
        <Card title={'给角色GM命令增加过滤器'} bordered={false}>
          <Form onSubmit={this.handleSubmit}>
            <FormItem
              label="角色"
              hasFeedback
            >
              {getFieldDecorator('roleId', {
                rules: [
                  { required: true, message: '请选择角色' },
                ],
              })(
                <Select allowClear={true} style={{ maxWidth: 286, width: '100%' }}
                        placeholder="请选择角色"
                        filterOption={(input, option) => option.props.value.toString()
                          .indexOf(input) === 0}
                >
                  {roleItems}
                </Select>
              )}
            </FormItem>
            <FormItem
              label="过滤器"
              hasFeedback
            >
              {getFieldDecorator('filterId', {
                rules: [
                  { required: true, message: '请选择过滤器' },
                ],
              })(
                <Select allowClear={true} style={{ maxWidth: 286, width: '100%' }}
                        placeholder="请选择过滤器"
                        filterOption={(input, option) => option.props.value.toString()
                          .indexOf(input) === 0}
                >
                  {filterItems}
                </Select>
              )}
            </FormItem>
            <FormItem
              label="GM命令名称"
              hasFeedback
            >
              {getFieldDecorator('commandName', {
                rules: [
                  { required: true, message: '请输入GM命令名称' },
                ],
              })(
                <Input placeholder="请输入GM命令名称">
                </Input>
              )}
            </FormItem>
            <FormItem
              label="参数名称"
              hasFeedback
            >
              {getFieldDecorator('paramName', {
                rules: [
                  { required: true, message: '请输入参数名称' },
                ],
              })(
                <Input placeholder="请输入参数名称">
                </Input>
              )}
            </FormItem>
            <FormItem>
              <Button type="primary" htmlType="submit">提交</Button>
            </FormItem>
          </Form>
        </Card>
      </>
    );

  };
}

export default GMForm;
