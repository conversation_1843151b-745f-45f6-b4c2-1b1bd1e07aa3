{"name": "ant-design-pro", "version": "1.1.0", "description": "An out-of-box UI solution for enterprise applications", "private": true, "scripts": {"precommit": "npm run lint-staged", "start": "cross-env ESLINT=none roadhog dev --no-eslint", "start:no-proxy": "cross-env NO_PROXY=true ESLINT=none roadhog dev", "build": "cross-env ESLINT=none roadhog build", "site": "roadhog-api-doc static && gh-pages -d dist", "analyze": "cross-env ANALYZE=true roadhog build", "lint:style": "stylelint \"src/**/*.less\" --syntax less", "lint": "eslint --ext .js src mock tests && npm run lint:style", "lint:fix": "eslint --fix --ext .js src mock tests && npm run lint:style", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js", "test": "roadhog test", "test:component": "roadhog test ./src/components", "test:all": "node ./tests/run-tests.js"}, "dependencies": {"history": "4.7.2", "@antv/data-set": "^0.8.0", "@babel/polyfill": "7.0.0-beta.36", "@babel/runtime": "7.0.0-beta.36", "@babel/core": "7.0.0-beta.36", "antd": "^3.1.0", "babel-runtime": "^6.9.2", "bizcharts": "3.1.3-beta.1", "bizcharts-plugin-slider": "^2.0.1", "classnames": "^2.2.5", "dva": "^2.1.0", "dva-loading": "^1.0.4", "enquire-js": "^0.1.1", "fastclick": "^1.0.6", "lodash": "^4.17.4", "lodash-decorators": "^4.4.1", "moment": "^2.19.1", "numeral": "^2.0.6", "omit.js": "^1.0.0", "path-to-regexp": "^2.1.0", "prop-types": "^15.5.10", "qs": "^6.5.0", "rc-drawer-menu": "^0.5.0", "react": "^16.2.0", "react-container-query": "^0.9.1", "react-document-title": "^2.0.3", "react-dom": "^16.2.0", "react-fittext": "^1.0.0", "eslint-plugin-compat": "2.5.1", "babel-preset-umi": "0.2.1", "eslint": "4.19.1", "babel-loader": "8.0.0-beta.1", "eslint-plugin-import": "2.6.0", "detect-port": "1.2.1", "umi-test": "0.3.0", "url-polyfill": "^1.0.10"}, "devDependencies": {"eslint-plugin-react": "7.4.0", "handlebars": "4.0.3", "eslint-config-airbnb": "16.1.0", "@types/react": "^16.0.38", "babel-eslint": "^8.1.2", "babel-plugin-dva-hmr": "^0.4.1", "babel-plugin-import": "^1.6.3", "babel-plugin-transform-decorators-legacy": "^1.3.4", "cross-env": "^5.1.1", "cross-port-killer": "^1.0.1", "enzyme": "^3.1.0", "gh-pages": "^1.0.0", "husky": "^0.14.3", "lint-staged": "^6.0.0", "mockjs": "^1.0.1-beta3", "pro-download": "^1.0.1", "redbox-react": "^1.5.0", "regenerator-runtime": "^0.11.1", "roadhog": "2.1.0", "roadhog-api-doc": "^0.3.4", "rollbar": "^2.3.4", "stylelint": "^13.8.0", "stylelint-config-standard": "^18.0.0"}, "optionalDependencies": {"nightmare": "^2.10.0"}, "lint-staged": {"**/*.{js,jsx}": "lint-staged:js", "**/*.less": "stylelint --syntax less"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}