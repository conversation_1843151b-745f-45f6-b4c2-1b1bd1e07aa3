@import "~antd/lib/style/themes/default.less";

.radar {
  .legend {
    margin-top: 16px;
    .legendItem {
      position: relative;
      text-align: center;
      cursor: pointer;
      color: @text-color-secondary;
      line-height: 22px;
      p {
        margin: 0;
      }
      h6 {
        color: @heading-color;
        padding-left: 16px;
        font-size: 24px;
        line-height: 32px;
        margin-top: 4px;
        margin-bottom: 0;
      }
      &:after {
        background-color: @border-color-split;
        position: absolute;
        top: 8px;
        right: 0;
        height: 40px;
        width: 1px;
        content: '';
      }
    }
    > :last-child .legendItem:after {
      display: none;
    }
    .dot {
      border-radius: 6px;
      display: inline-block;
      margin-right: 6px;
      position: relative;
      top: -1px;
      height: 6px;
      width: 6px;
    }
  }
}
