import request from '../utils/request';

export async function query() {
  return request('/api/validParamCollections');
}

export async function createValidParamCollection(params) {
  return request('/validParamCollections', {
    method: 'POST',
    body: params,
  });
}

export async function updateValidParamCollection(validParamCollectionId, params) {
  return request('/validParamCollections/' + validParamCollectionId, {
    method: 'POST',
    body: params,
  });
}

export async function deleteValidParamCollection(validParamCollectionId) {
  return request('/api/validParamCollections/' + validParamCollectionId, {
    method: 'DELETE',
  });
}

export async function findValidParamCollection(validParamCollectionId) {
  return request('/api/validParamCollections/' + validParamCollectionId);
}

