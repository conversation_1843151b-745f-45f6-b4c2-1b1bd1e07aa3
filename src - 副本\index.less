html,
body,
:global(#root) {
  height: 100%;
}

:global(.ant-layout) {
  min-height: 100%;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.globalSpin {
  width: 100%;
  margin: 40px 0 !important;
}

// temp fix for https://github.com/ant-design/ant-design/commit/a1fafb5b727b62cb0be29ce6e9eca8f579d4f8b7
:global {
  .ant-spin-container {
    overflow: visible !important;
  }
}
