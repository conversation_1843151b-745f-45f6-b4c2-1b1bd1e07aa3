import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Card, Form, Input, Modal, Divider, Upload, Icon } from 'antd';
import { Link } from 'dva/router';
import StandardTable from '../../components/StandardTable/index';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';


const FormItem = Form.Item;

const CreateForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item, fileList, handleBeforeUpload } = props;
  const okHandle = () => {
    form.validateFields((err, values) => {
      if (err) return;
      // const formData = new FormData();
      const file = fileList[0];
      const params = {
        file: file,
        ...values
      };
      handleOk(params);
    });
  };
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 },
  };

  return (
    <Modal
      title={title}
      visible={modalVisible}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="CSV范围名称"
      >
        {form.getFieldDecorator('name', {
          rules: [{ required: true, message: 'CSV范围名称' }],
          initialValue: item && item.name
        })(
          <Input placeholder="CSV范围名称"/>
        )}
      </FormItem>
      <FormItem
        {...formItemLayout}
        label="上传文件"
      >
        {form.getFieldDecorator('upload', {})(
          <Upload action='' beforeUpload={handleBeforeUpload}
                  fileList={fileList}>
            <Button>
              <Icon type="upload"/> 上传文件(CSV)
            </Button>
          </Upload>
        )}
      </FormItem>
    </Modal>
  );
});

@connect(({ validParamCollection }) => ({
  validParamCollection,
}))
@Form.create()
export default class TableList extends PureComponent {
  state = {
    modalVisible: false,
    expandForm: false,
    selectedRows: [],
    formValues: {},
    modal: {
      modalVisible: false,
      fileList: [],
    }
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'validParamCollection/fetchValidParamCollections',
    });
  }

  handleCreateSubmit = (data) => {
    const formData = new FormData();
    const payload = data;
    formData.append('file', payload.file);
    formData.append('name', payload.name);
    this.props.dispatch({
      type: 'validParamCollection/createValidParamCollection',
      payload: formData,
    });
  };

  handleEditSubmit = (validParamCollectionId, data) => {
    const formData = new FormData();
    const payload = data;
    formData.append('file', payload.file);
    formData.append('name', payload.name);
    this.props.dispatch({
      type: 'validParamCollection/createValidParamCollection',
      payload: formData,
      validParamCollectionId: validParamCollectionId
    });
  };

  handleEditClick = item => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data) => this.handleEditSubmit(item.id, data),
        handleBeforeUpload: (file) => this.handleBeforeUpload(file),
        handleCancel: this.handleModalCancel,
        fileList: [],
        item: item,
        title: '编辑'
      }
    });
  };

  handleBeforeUpload = (file) => {
    this.setState(({ modal }) => (
      {
        modal:{
          ...modal,
          fileList: [file],
        }
      }
    ));
    return false;
  };


  handleModalCancel = () => {
    this.setState({
      modal: {
        modalVisible: false,
      }
    });
  };

  handleDeleteUser = validParamCollectionId => {
    this.props.dispatch({
      type: 'validParamCollection/deleteValidParamCollection',
      validParamCollectionId: validParamCollectionId
    });
  };

  handleSelectRows = (rows) => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleModalVisible = () => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data) => this.handleCreateSubmit(data),
        handleCancel: this.handleModalCancel,
        handleBeforeUpload: (file) => this.handleBeforeUpload(file),
        fileList: [],
        title: '创建'
      }
    });
  };


  render() {
    const { validParamCollection: { validParamCollections }, } = this.props;
    const { selectedRows, modal } = this.state;
    const columns = [
      {
        title: '参数选择范围名称',
        dataIndex: 'name',
      },
      {
        title: '操作',
        render: item => <Fragment>
          <a onClick={() => this.handleDeleteUser(item.id)}>删除</a>
          <Divider type="vertical"/>
          <a onClick={() => this.handleEditClick(item)}>编辑</a>
        </Fragment>
      },
    ];

    return (
      <PageHeaderLayout title="查询表格">
        <Card bordered={false}>
          <div>
            <div>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible(true)}>
                新建
              </Button>
            </div>
            <StandardTable
              selectedRows={selectedRows}
              data={{ list: validParamCollections }}
              columns={columns}
              onSelectRow={this.handleSelectRows}
            />
          </div>
        </Card>
        <CreateForm {...modal}/>
      </PageHeaderLayout>
    );
  }
}
