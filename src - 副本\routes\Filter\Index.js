import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Card, Form, Input, Modal, InputNumber, Select, Divider } from 'antd';
import { Link } from 'dva/router';
import StandardTable from '../../components/StandardTable/index';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';


const FormItem = Form.Item;

const RangeForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item, select_type } = props;
  const okHandle = () => {
    form.validateFields((err, values) => {
      if (err) return;
      handleOk({...values, type: "RANGE"});
    });
  };

  return (
    <Modal
      title={title}
      visible={modalVisible && select_type === "RANGE"}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="过滤器名称"
      >
        {form.getFieldDecorator('name', {
          rules: [{ required: true, message: '过滤器名称' }],
        })(
          <Input placeholder="过滤器名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="最小值"
      >
        {form.getFieldDecorator('min', {
          rules: [{ required: true, message: '最小值' }],
        })(
          <InputNumber placeholder="最小值"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="最大值"
      >
        {form.getFieldDecorator('max', {
          rules: [{ required: true, message: '最大值' }],
        })(
          <InputNumber placeholder="最大值"/>
        )}
      </FormItem>
    </Modal>
  );
});

const SplitForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item, select_type, filters } = props;
  const okHandle = () => {
    form.validateFields((err, values) => {
      if (err) return;
      handleOk({...values, type: "SPLIT"});
    });
  };

  const filterItems = filters.map((filter, index) =>
    <Select.Option key={index} value={filter.id}>
      {filter.name}
    </Select.Option>);

  return (
    <Modal
      title={title}
      visible={modalVisible && select_type === "SPLIT"}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="过滤器名称"
      >
        {form.getFieldDecorator('name', {
          rules: [{ required: true, message: '过滤器名称' }],
        })(
          <Input placeholder="过滤器名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="分隔符"
      >
        {form.getFieldDecorator('split', {
          rules: [{ required: true, message: '分隔符' }],
        })(
          <Input placeholder="分隔符"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="子过滤器"
      >
        {form.getFieldDecorator('nextFilterId', {
          rules: [{ required: true, message: '请选择子过滤器' }],
        })(
          <Select allowClear={true} style={{ maxWidth: 286, width: '100%' }}
                  placeholder="请选择子过滤器"
                  filterOption={(input, option) => option.props.value.toString()
                    .indexOf(input) === 0}
          >
            {filterItems}
          </Select>
        )}
      </FormItem>
    </Modal>
  );
});

const ModForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item, select_type, filters } = props;
  const okHandle = () => {
    form.validateFields((err, values) => {
      if (err) return;
      handleOk({...values, type: "MOD"});
    });
  };

  const filterItems = filters.map((filter, index) =>
    <Select.Option key={index} value={filter.id}>
      {filter.name}
    </Select.Option>);

  return (
    <Modal
      title={title}
      visible={modalVisible && select_type === "MOD"}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="过滤器名称"
      >
        {form.getFieldDecorator('name', {
          rules: [{ required: true, message: '过滤器名称' }],
        })(
          <Input placeholder="过滤器名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="每几个选择"
      >
        {form.getFieldDecorator('mod', {
          rules: [{ required: true, message: '每几个选择' }],
        })(
          <InputNumber placeholder="每几个选择"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="偏移量（没有为0）"
      >
        {form.getFieldDecorator('off', {
          rules: [{ required: true, message: '偏移量（没有为0）' }],
        })(
          <InputNumber placeholder="偏移量（没有为0）"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="子过滤器"
      >
        {form.getFieldDecorator('nextFilterId', {
          rules: [{ required: true, message: '请选择子过滤器' }],
        })(
          <Select allowClear={true} style={{ maxWidth: 286, width: '100%' }}
                  placeholder="请选择子过滤器"
                  filterOption={(input, option) => option.props.value.toString()
                    .indexOf(input) === 0}
          >
            {filterItems}
          </Select>
        )}
      </FormItem>
    </Modal>
  );
});

const CSVForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item, fileList, select_type, validParamCollections } = props;
  const okHandle = () => {
    form.validateFields((err, values) => {
      if (err) return;
      handleOk({...values, type: "CSV"});
    });
  };
  const filterItems = validParamCollections.map((validParamCollection, index) =>
    <Select.Option key={index} value={validParamCollection.id}>
      {validParamCollection.name}
    </Select.Option>);


  return (
    <Modal
      title={title}
      visible={modalVisible && select_type === "CSV"}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="过滤器名称"
      >
        {form.getFieldDecorator('name', {
          rules: [{ required: true, message: '过滤器名称' }],
          initialValue: item && item.name
        })(
          <Input placeholder="过滤器名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="参数集"
      >
        {form.getFieldDecorator('validParamCollectionId', {
          rules: [{ required: true, message: '请选择参数集' }],
        })(
          <Select allowClear={true} style={{ maxWidth: 286, width: '100%' }}
                  placeholder="请选择参数集"
                  filterOption={(input, option) => option.props.value.toString()
                    .indexOf(input) === 0}
          >
            {filterItems}
          </Select>
        )}
      </FormItem>
    </Modal>
  );
});

@connect(({ paramFilter, validParamCollection }) => ({
  paramFilter, validParamCollection
}))
@Form.create()
export default class TableList extends PureComponent {
  state = {
    modalVisible: false,
    expandForm: false,
    selectedRows: [],
    formValues: {},
    modal: {
      modalVisible: false,
    }
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'paramFilter/fetchParamFilter',
    });
    dispatch({
      type: 'validParamCollection/fetchValidParamCollections',
    });
  }

  handleCreateSubmit = (data) => {
    this.props.dispatch({
      type: 'paramFilter/createParamFilter',
      payload: data,
    });
  };

  handleModalCancel = () => {
    this.setState({
      modal: {
        modalVisible: false,
      }
    });
  };

  handleDeleteUser = paramFilterId => {
    this.props.dispatch({
      type: 'paramFilter/deleteParamFilter',
      paramFilterId: paramFilterId
    });
  };

  handleSelectRows = (rows) => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleModalVisible = (type) => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data) => this.handleCreateSubmit(data),
        handleCancel: this.handleModalCancel,
        title: '创建',
        select_type: type
      }
    });
  };


  render() {
    const { paramFilter: { paramFilters }, validParamCollection: {validParamCollections}} = this.props;
    const { selectedRows, modal } = this.state;
    const columns = [
      {
        title: '过滤器名称',
        dataIndex: 'name',
      },
      {
        title: '操作',
        render: item => <Fragment>
          <a onClick={() => this.handleDeleteUser(item.id)}>删除</a>
        </Fragment>
      },
    ];

    return (
      <PageHeaderLayout title="查询表格">
        <Card bordered={false}>
          <div>
            <div>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible("RANGE")}>
                新建范围过滤器
              </Button>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible("CSV")}>
                新建参数集合过滤器
              </Button>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible("SPLIT")}>
                新建分隔过滤器
              </Button>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible("MOD")}>
                新建偏移选中过滤器
              </Button>
            </div>
            <StandardTable
              selectedRows={selectedRows}
              data={{ list: paramFilters }}
              columns={columns}
              onSelectRow={this.handleSelectRows}
            />
          </div>
        </Card>
        <RangeForm {...modal} filters={paramFilters}/>
        <SplitForm {...modal} filters={paramFilters}/>
        <ModForm {...modal} filters={paramFilters}/>
        <CSVForm {...modal} filters={paramFilters} validParamCollections={validParamCollections}/>
      </PageHeaderLayout>
    );
  }
}
