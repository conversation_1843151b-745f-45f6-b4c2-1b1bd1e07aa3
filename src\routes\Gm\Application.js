import React, { Component } from 'react';
import { connect } from 'dva';
import { <PERSON><PERSON>, <PERSON>, DatePicker, Form, Input, Select, Switch, TimePicker } from 'antd';
import GMForm from '../Gm/GMForm';

const { Option } = Select;
@connect(({ gm, loading }) => {
  window.loading = loading;
  return {
    gm,
    loading: loading.effects['gm/fetchServers'],
  };
})
export default class Application extends Component {
  state = {
    schedule: false,
  };

  handleSumbitClick = (e) => {

  };

  componentDidMount() {
    this.props.dispatch({
      type: 'gm/fetchServers',
    });
  }

  componentWillUnmount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'chart/clear',
    });
  }


  render() {
    const { gm: { servers, modules }, loading } = this.props;
    let commandId = this.props.match.params.commandId;

    let command;
    modules.forEach(m=>m.commands.forEach(c=>{if(c.id === commandId){
      command = c
    }}));
    const paramsContent = command.params.length > 0 ? (
      <Form>

          {
            command.params.map((c, index) =><Form.Item key={index}> <Input key={index}/></Form.Item>)
          }


      </Form>
    ) : null;

    const xx = paramsContent ? Form.create({})(paramsContent) : null;

    const selectServer = servers.length > 0 ? (
      <div>选择服务器
        <Select style={{ maxWidth: 286, width: '100%' }}
                mode="multiple"
                placeholder="请选择服务器">
          {
            servers.map((server, index) => <Option key={index} value={server.id}>{server.name}</Option>)

          }
        </Select>
      </div>
    ) : null;

    return (
      <>
      <GMForm command={command}/>
      {/*<Card bordered={false} bodyStyle={{ padding: 0 }}>*/}
        {/*{xx}*/}
      {/*</Card>*/}
      <Card loading={loading} bordered={false} bodyStyle={{ padding: 0 }}>
        {selectServer}
      </Card>


      <div>定时任务
        <Switch checkedChildren="开" unCheckedChildren="关" defaultChecked>
        </Switch>
        <DatePicker></DatePicker>
        <TimePicker/>
      </div>
      <Button type="primary" onClick={this.handleSumbitClick}>提交</Button>
      </>
    );
  }
}
