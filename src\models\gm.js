import {
  queryModules,
  queryServers,
  doCommand,
  doCommandSchedule, // 1. 引入新的 service 方法
  getCommand,
  patchCommand,
  doCommandByName,
  doPlayerOnlineGift,
  queryPlayerOnlineGiftRecord,
  deletePlayerOnlineGift,
  updateCommands,
  queryScheduleJobs,
  deleteScheduleJob,
} from '../services/gm';

export default {
  namespace: 'gm',

  state: {
    servers: [],
    modules: [],
    playerOnlineGiftRecord: {
      list: [],
      pagination: {},
    },
    router: [{ name: 'GM模块', path: 'gm/modules' }],
    wgs: 'RELEASE',
  },

  effects: {
    * fetchModules(_, { call, put }) {
      const response = yield call(queryModules);
      yield put({
        type: 'saveModules',
        payload: response,
      });
    },
    * fetchServers(_, { call, put, select }) {
      const state = yield select(state => state.gm);
      const response = yield call(queryServers, state.wgs);
      yield put({
        type: 'saveServers',
        payload: response,
      });
    },
    * fetchCommandById({ commandId }, { call, put }) {
      const response = yield call(getCommand, commandId);
      return response;
    },
    * doCommand({ commandName, payload }, { call, put, select }) {
      const { wgs } = yield select(state => state.gm);
      const response = yield call(doCommand, commandName, { ...payload.data, wgs: wgs });

      return response;
    },

    // ------------------- 新增的 Effect -------------------
    /**
     * 处理定时任务的 Effect
     */
    * doCommandSchedule({ commandName, payload }, { call, put, select }) {
      const { wgs } = yield select(state => state.gm);
      // 调用 service 中新添加的 doCommandSchedule 方法
      const response = yield call(doCommandSchedule, { ...payload.data, wgs: wgs });

      // 根据您的回调函数逻辑，这里应该返回一个布尔值
      // 请根据您后端 API 的实际返回结构调整，这里假设返回 { success: true/false }
      return response && response.success;
    },
    // ----------------------------------------------------

    // ------------------- 为定时任务列表页新增的 Effects -------------------
    * fetchScheduleJobs(_, { call, put }) {
      const response = yield call(queryScheduleJobs);
      if (response) {
        yield put({
          type: 'saveScheduleJobs',
          payload: response,
        });
      }
    },

    * deleteScheduleJob({ jobId }, { call, put }) {
      // 调用删除接口
      const response = yield call(deleteScheduleJob, jobId);
      if (response && response.success) {
        // 删除成功后，重新获取列表以刷新界面
        yield put({ type: 'fetchScheduleJobs' });
      }
      return response;
    },

    * doCommandByName({ commandId, payload }, { call, put, select }) {
      const { wgs } = yield select(state => state.gm);
      const response = yield call(doCommandByName, { ...payload, wgs: wgs });
      return response;
    },
    * patchCommandById({ commandId, payload }, { call, put, select }) {
      const response = yield call(patchCommand, commandId, payload);
      yield put({ type: 'fetchModules' });
      return response;
    },
    * PlayerOnlineGift({ formData }, { call, put, select }) {
      const { wgs } = yield select(state => state.gm);
      formData.append('wgs', wgs);
      const response = yield call(doPlayerOnlineGift, formData);

      return response;
    },
    * fetchPlayerOnlineGiftRecord({ payload }, { call, put }) {
      const response = yield call(queryPlayerOnlineGiftRecord, payload);
      if (response) {
        yield put({
          type: 'savePlayerOnlineGiftRecord',
          payload: response,
        });
      }
    },
    * deletePlayerOnlineGift({ playerOnlineGiftId }, { call, put, select }) {
      const response = yield call(deletePlayerOnlineGift, playerOnlineGiftId);
      return response;
    },
    * changeWgs({ payload }, { call, put, select }) {
      yield put({
        type: 'saveWgs',
        payload,
      });
      yield put({ type: 'fetchServers' });
    },
    * updateCommands({ serverId }, { call, put, select }) {
      yield call(updateCommands, serverId);
    },
  },

  reducers: {
    saveModules(state, action) {
      var router = action.payload.map(a => {
        return {
          name: a.name,
          path: a.name,
          children: a.commands.map(c => {
            return { name: c.name, path: '/gm/commands/' + c.id };
          }),
        };
      });
      return {
        ...state,
        modules: action.payload,
        router: [{ name: 'GM模块', path: 'gm/modules', children: router }],
      };
    },
    saveServers(state, action) {
      return {
        ...state,
        servers: action.payload,
      };
    },
    commandResult(state, action) {
      return {
        ...state,
        commandResult: action.payload,
      };
    },
    aveScheduleJobs(state, action) {
      return {
        ...state,
        scheduleJobs: action.payload,
      };
    },
    savePlayerOnlineGiftRecord(state, action) {
      return {
        ...state,
        playerOnlineGiftRecord: action.payload,
      };
    },
    saveWgs(state, action) {
      return {
        ...state,
        wgs: action.payload,
      };
    },
  },
};