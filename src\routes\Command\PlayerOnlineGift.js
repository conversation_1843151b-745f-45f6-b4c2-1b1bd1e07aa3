import React, { Component } from 'react';
import { connect } from 'dva';
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Form, Icon, Input, Modal, Row, Upload } from 'antd';

const FormItem = Form.Item;

@connect(({ loading }) => ({
  submitting: loading.effects['gm/PlayerOnlineGift'],
}))
@Form.create()
class PlayerOnlineGift extends Component {
  state = {
    payload: {},
    fileList: [],
    commandResult: null,
    showConfirm: false,
  };

  handleBeforeUpload = (file) => {
    this.setState(({ fileList }) => ({
      fileList: [file],
    }));
    return false;
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { fileList } = this.state;
        // const formData = new FormData();
        const file = fileList[0];
        const params = {
          file: file,
          ...values
        };

        this.setState({
          payload: params,
          showConfirm: true,
          commandResult: null,
        });
      }
    });
  };
  handleCallBack = data => {
    this.setState({
      commandResult: data === true ? '执行成功' : data
    });
  };

  handleClearResult = () => {
    this.setState({
      commandResult: null
    });
  };

  handleConfirmOk = () => {
    const formData = new FormData();
    const payload = this.state.payload;
    formData.append('files', payload.file);
    formData.append('sendType', payload.sendType);
    formData.append('title', payload.title);
    formData.append('content', payload.content);
    formData.append('startTime', payload.startTime.toDate()
      .getTime());
    formData.append('endTime', payload.endTime.toDate()
      .getTime());
    formData.append('minLevel', payload.minLevel);
    formData.append('maxLevel', payload.maxLevel);
    formData.append('minVipLevel', payload.minVipLevel);
    formData.append('maxVipLevel', payload.maxVipLevel);
    formData.append('platform', payload.platform);
    this.props.dispatch({
      type: 'gm/PlayerOnlineGift',
      formData: formData,
    })
      .then(this.handleCallBack);


    this.setState({
      payload: {},
      showConfirm: false
    });
  };

  handleConfirmCancel = () => {
    this.setState({
      payload: {},
      showConfirm: false,
      commandResult: null,
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { submitting } = this.props;
    const formItemLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
    };

    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 0,
        },
        sm: {
          span: 16,
          offset: 8,
        },
      },
    };
    const commandResult = this.state.commandResult;
    const renderCommandResult = commandResult !== undefined && commandResult !== null ? commandResult.sort((a, b) => a.serverId - b.serverId)
      .map(a => (
        <Row gutter={16} key={a.serverId}>
          <Col span={6}>{a.serverId}</Col>
          <Col span={18}>
            <pre>{a.result}</pre>
          </Col>
        </Row>
      )) : commandResult;

    return (
      <>
        <Form onSubmit={this.handleSubmit}>
          <FormItem
            {...formItemLayout}
            label="发放类型"
            hasFeedback
          >
            {getFieldDecorator('sendType', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input placeholder="Please select a country">
              </Input>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="标题"
            hasFeedback
          >
            {getFieldDecorator('title', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input placeholder="Please select a country">
              </Input>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="邮件内容"
            hasFeedback
          >
            {getFieldDecorator('content', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input.TextArea autosize={true} placeholder="Please select a country">
              </Input.TextArea>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="上传文件"
          >
            {getFieldDecorator('upload', {
              // getValueFromEvent: this.normFile,
            })(
              <Upload action='' beforeUpload={this.handleBeforeUpload}
                      fileList={this.state.fileList}>
                <Button>
                  <Icon type="upload"/> 上传文件(CSV)
                </Button>
              </Upload>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="领奖开始时间"
            hasFeedback
          >
            {getFieldDecorator('startTime', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="Select Time"
              />
            )}
          </FormItem>
          <FormItem
            {...formItemLayout}
            label="领奖结束时间"
            hasFeedback
          >
            {getFieldDecorator('endTime', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="Select Time"
              />
            )}
          </FormItem>
          <FormItem
            {...formItemLayout}
            label="最低等级"
            hasFeedback
          >
            {getFieldDecorator('minLevel', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input placeholder="Please select a country">
              </Input>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="最高等级"
            hasFeedback
          >
            {getFieldDecorator('maxLevel', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input placeholder="Please select a country">
              </Input>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="Vip最低等级"
            hasFeedback
          >
            {getFieldDecorator('minVipLevel', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input placeholder="Please select a country">
              </Input>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="Vip最高等级"
            hasFeedback
          >
            {getFieldDecorator('maxVipLevel', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input placeholder="Please select a country">
              </Input>
            )}
          </FormItem>

          <FormItem
            {...formItemLayout}
            label="渠道"
            hasFeedback
          >
            {getFieldDecorator('platform', {
              rules: [
                { required: true, message: 'Please select your country!' },
              ],
            })(
              <Input placeholder="Please select a country">
              </Input>
            )}
          </FormItem>

          <FormItem {...tailFormItemLayout}>
            <Button type="primary" htmlType="submit" loading={submitting}>提交</Button>
          </FormItem>
        </Form>
        <Modal
          title="最终确认"
          visible={this.state.showConfirm}
          onOk={this.handleConfirmOk}
          onCancel={this.handleConfirmCancel}
        >
          <Row>
            <Col span={6}>发放类型：</Col>
            <Col span={14}>{this.state.payload.sendType}</Col>
          </Row>
          <Row>
          <Col span={6}>邮件标题：</Col>
          <Col span={14}>{this.state.payload.title}</Col>
          </Row>
          <Row>
          <Col span={6}>邮件内容：</Col>
          <Col span={14}>{this.state.payload.content}</Col>
          </Row>
          {/*<Row>*/}
          {/*<Col span={6}>上传文件：</Col>*/}
          {/*<Col span={14}>{this.state.payload.file}</Col>*/}
          {/*</Row>*/}
          <Row>
          <Col span={6}>领奖开始时间：</Col>
          <Col span={14}>{this.state.payload.endTime && this.state.payload.startTime.format("YYYY-MM-DD HH:mm:ss") || "任意"}</Col>
          </Row>
          <Row>
          <Col span={6}>领奖结束时间：</Col>
          <Col span={14}>{this.state.payload.endTime && this.state.payload.endTime.format("YYYY-MM-DD HH:mm:ss") || "任意"}</Col>
          </Row>
          <Row>
          <Col span={6}>最低等级：</Col>
          <Col span={14}>{this.state.payload.minLevel}</Col>
          </Row>
          <Row>
          <Col span={6}>最高等级：</Col>
          <Col span={14}>{this.state.payload.maxLevel}</Col>
          </Row>
          <Row>
          <Col span={6}>VIP最低等级：</Col>
          <Col span={14}>{this.state.payload.minVipLevel}</Col>
          </Row>
          <Row>
          <Col span={6}>VIP最高等级：</Col>
          <Col span={14}>{this.state.payload.maxVipLevel}</Col>
          </Row>

          <Row>
          <Col span={6}>渠道：</Col>
          <Col span={14}>{this.state.payload.platform}</Col>
          </Row>
        </Modal>


        <Card title="执行结果" bordered={false}
              extra={<Icon type="close-circle-o" onClick={this.handleClearResult}/>}>
          <div>
            {renderCommandResult}
          </div>
        </Card>
      </>
    );
  }
}

export default PlayerOnlineGift;
