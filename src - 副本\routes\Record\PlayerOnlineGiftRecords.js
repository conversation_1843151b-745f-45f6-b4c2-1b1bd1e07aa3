import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Card, Divider, Modal, Table } from 'antd';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';
import moment from 'moment/moment';


const getValue = obj => Object.keys(obj)
  .map(key => obj[key])
  .join(',');
const statusMap = ['default', 'processing', 'success', 'error'];
const status = ['关闭', '运行中', '已上线', '异常'];
@connect(({ gm, loading }) => ({
  gm,
  loading: loading.models.rule,
}))
export default class TableList extends PureComponent {
  state = {
    modalVisible: false,
    expandForm: false,
    visible: false,
    selectedRecordId: null,
    selectedRows: [],
    formValues: {},
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'gm/fetchPlayerOnlineGiftRecord',
    });
  }

  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { formValues } = this.state;

    const filters = Object.keys(filtersArg)
      .reduce((obj, key) => {
        const newObj = { ...obj };
        newObj[key] = getValue(filtersArg[key]);
        return newObj;
      }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...formValues,
      ...filters,
    };
    if (sorter.field) {
      params.sorter = `${sorter.field}_${sorter.order}`;
    }

    dispatch({
      type: 'gm/fetchPlayerOnlineGiftRecord',
      payload: params,
    });
  };

  handleOk = ()=>{
    const { dispatch } = this.props;
    const { selectedRecordId } = this.state;
    dispatch({
      type: 'gm/deletePlayerOnlineGift',
      playerOnlineGiftId: selectedRecordId,
    });
    this.setState({
      visible: false,
      selectedRecordId: null,
    });
  };

  handleCancel = ()=>{
    this.setState({
      visible: false,
      selectedRecordId: null,
    });
  };

  handleSelectedRecord = (key)=>{
    this.setState({
      visible: true,
      selectedRecordId: key,
    });
  };

  render() {
    const columns = [
      {
        title: 'id',
        dataIndex: 'id',
      },
      {
        title: '类型',
        dataIndex: 'detail.sendType',
      },
      {
        title: '邮件标题',
        dataIndex: 'detail.title',
      },
      // {
      //   title: '邮件内容',
      //   dataIndex: 'time',
      //   key: 'time',
      //   sorter: true,
      // },
      {
        title: '领奖开始时间',
        dataIndex: 'detail.startTime',
        render: val => <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '领奖结束时间',
        dataIndex: 'detail.endTime',
        render: val => <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '需求人',
        dataIndex: 'creator',
      },
      {
        title: '状态',
        dataIndex: 'deleted',
        render: val => <span>{val ? "已删除":"正常"}</span>,
      },
      {
        title: '操作',
        render: (record) => (
          <Fragment>
            <a >详情</a>
            <Divider type="vertical" />
            <a onClick={e => this.handleSelectedRecord(record.id)}>删除</a>
          </Fragment>
        ),
      },
    ];


    const { gm: { playerOnlineGiftRecord } } = this.props;
    // const expandedRowRender = item => {
    //   const data = item.msg;
    //   const columns = [
    //     { title: '服务器ID', dataIndex: 'serverId', key: 'serverId',width: 100 },
    //     { title: '执行结果', dataIndex: 'result', key: 'result' },
    //   ];
    //   return (
    //     <Table
    //       columns={columns}
    //       dataSource={data}
    //       pagination={false}
    //     />
    //   );
    // };

    return (
      <PageHeaderLayout title="角色批量发奖记录">
        <Card bordered={false} title={'角色批量发奖记录'}>
          <div>
            <Table
              rowKey={'id'}
              // rowSelection={rowSelection}
              dataSource={playerOnlineGiftRecord.list}
              columns={columns}
              pagination={playerOnlineGiftRecord.pagination}
              onChange={this.handleStandardTableChange}
              // expandedRowRender={expandedRowRender}
            />
          </div>
        </Card>

        <Modal
          title="删除确认"
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <p>确认删除</p>
        </Modal>
      </PageHeaderLayout>
    );
  }
}
