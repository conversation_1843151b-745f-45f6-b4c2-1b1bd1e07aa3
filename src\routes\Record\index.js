import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Card, Table } from 'antd';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';


const getValue = obj => Object.keys(obj)
  .map(key => obj[key])
  .join(',');
const statusMap = ['default', 'processing', 'success', 'error'];
const status = ['关闭', '运行中', '已上线', '异常'];
const columns = [
  {
    title: '用户',
    dataIndex: 'owner.name',
    key: 'username',
  },
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time',
    sorter: true,
  },
  {
    title: '命令',
    dataIndex: 'command',
    key: 'cmd',
  },
];

@connect(({ record, loading }) => ({
  record,
  loading: loading.models.rule,
}))
export default class TableList extends PureComponent {
  state = {
    modalVisible: false,
    expandForm: false,
    selectedRows: [],
    formValues: {},
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'record/fetchRecord',
    });
  }

  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { formValues } = this.state;

    const filters = Object.keys(filtersArg)
      .reduce((obj, key) => {
        const newObj = { ...obj };
        newObj[key] = getValue(filtersArg[key]);
        return newObj;
      }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...formValues,
      ...filters,
    };
    if (sorter.field) {
      params.sorter = `${sorter.field}_${sorter.order}`;
    }

    dispatch({
      type: 'record/fetchRecord',
      payload: params,
    });
  };

  render() {
    const { record: { data } } = this.props;

    const expandedRowRender = item => {
      const data = item.msg;
      const columns = [
        { title: '服务器ID', dataIndex: 'serverId', key: 'serverId',width: 100 },
        { title: '执行结果', dataIndex: 'result', key: 'result' },
      ];
      return (
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
        />
      );
    };

    return (
      <PageHeaderLayout title="查询表格">
        <Card bordered={false} title={'用户操作记录'}>
          <div>
            <Table
              rowKey={'id'}
              // rowSelection={rowSelection}
              dataSource={data.list}
              columns={columns}
              pagination={data.pagination}
              onChange={this.handleStandardTableChange}
              expandedRowRender={expandedRowRender}
            />
          </div>
        </Card>
      </PageHeaderLayout>
    );
  }
}
