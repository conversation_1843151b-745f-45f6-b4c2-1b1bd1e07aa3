import {query, createParamFilter, deleteParamFilter, addParamFilter} from '../services/ParamFilter';

export default {
  namespace: 'paramFilter',

  state: { paramFilters: [] },

  effects: {
    * fetchParamFilter(payload, { call, put }) {
      const response = yield call(query);
      let paramFilters = [];
      Object.keys(response._embedded).forEach(key=>{
        let f = response._embedded[key].map(paramFilter=>{
          let s = paramFilter._links.self.href.split('/');
          paramFilter.id = s[s.length - 1];
          return paramFilter;
        })
        Array.prototype.push.apply(paramFilters, f);
      });
      // const paramFilters = response._embedded.abstractParamFilters.map(paramFilter=>{
      //   let s = paramFilter._links.self.href.split('/');
      //   paramFilter.id = s[s.length - 1];
      //   return paramFilter;
      // });
      yield put({
        type: 'saveRole',
        payload: paramFilters,
      });
    },
    * createParamFilter({ payload }, { call, put }) {
      yield call(createParamFilter, payload);
      const response = yield put({ type: 'fetchParamFilter'});
      return response
    },
    * deleteParamFilter({ paramFilterId, payload }, { call, put }) {
      yield call(deleteParamFilter, paramFilterId);
      const response = yield put({ type: 'fetchParamFilter'});
      return response
    },
    * addParamFilter({ payload }, { call, put }) {
      yield call(addParamFilter, payload);
    },
  },

  reducers: {
    saveRole(state, action) {
      return {
        ...state,
        paramFilters: action.payload,
      };
    },
  },
};
