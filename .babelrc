{"presets": [["@babel/preset-env", {"targets": {"browsers": ["last 2 versions", "ie >= 9"]}}], "@babel/preset-react"], "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-object-rest-spread", "babel-plugin-transform-decorators-legacy", ["import", {"libraryName": "antd", "libraryDirectory": "es", "style": true}]], "env": {"development": {"plugins": ["dva-hmr"]}}}