import React, { Component } from 'react';
import { connect } from 'dva';
import { Button, Divider, Form, Input, Modal, Tree, } from 'antd';

const TreeNode = Tree.TreeNode;
const FormItem = Form.Item;

const CreateForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item } = props;
  const okHandle = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      handleOk(fieldsValue);
    });
  };
  return (
    <Modal
      title={title}
      visible={modalVisible}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="名字"
      >
        {form.getFieldDecorator('name', {
          rules: [{ required: true, message: 'Please input some description...' }],
          initialValue: item && item.name
        })(
          <Input placeholder="请输入菜单名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="名字"
      >
        {form.getFieldDecorator('path', {
          rules: [{ required: true, message: 'Please input some description...' }],
          initialValue: item && item.path
        })(
          <Input placeholder="请输入菜单路径"/>
        )}
      </FormItem>
    </Modal>
  );
});

@connect(({ menu, loading }) => ({
  menu,
  loading: loading.models.rule,
}))
export default class TableList extends Component {
  state = {
    modalVisible: false,
    expandForm: false,
    selectedRows: [],
    formValues: {},
  };

  handleEditSubmit = (menuId, data) => {
    this.props.dispatch({
      type: 'menu/updateMenu',
      payload: data,
      menuId: menuId
    });
  };
  handleCreateSubmit = (menuId, data) => {
    this.props.dispatch({
      type: 'menu/createMenu',
      payload: {...data, parentId: menuId},

    });
  };
  handleModalCancel = () => {
    this.setState({
      modal: {
        modalVisible: false,
      }
    });
  };
  handleEditClick = item => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data)=>this.handleEditSubmit(item.id, data),
        handleCancel: this.handleModalCancel,
        item: item,
        title: '编辑'
      }
    });
  };
  handleDeleteClick = item => {
  };
  handleAddClick = item => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data)=>this.handleCreateSubmit(item && item.id || null, data),
        handleCancel: this.handleModalCancel,
        item: null,
        title: '新建'
      }
    });
  };

  componentDidMount() {
    this.props.dispatch({
      type: 'menu/fetchMenu',
      payload: {
      },
    });
  }

  render() {
    const { menu: { menus }, loading } = this.props;
    const { modal } = this.state;
    const loop = (parentKey, data) => data.map((item, index) => {
      let key = parentKey + '-' + index;
      return <TreeNode key={key}
                       title={<div>
                         {item.name}
                         <Divider type="vertical"/>
                         <a onClick={() => this.handleEditClick(item)}>编辑</a>
                         <Divider type="vertical"/>
                         <a onClick={() => this.handleDeleteClick(item)}>删除</a>
                         <Divider type="vertical"/>
                         <a onClick={() => this.handleAddClick(item)}>添加</a>
                       </div>
                       }
      >{(item.children && item.children.length) ? loop(key, item.children) : null}</TreeNode>;
    });


    return (
      <>
        <Button onClick={() => this.handleAddClick()}>新建</Button>
        <Tree className="draggable-tree">
          {loop('0', menus)}
        </Tree>
        <CreateForm {...modal}/>
      </>

    );
  }
}
