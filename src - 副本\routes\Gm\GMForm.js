import React, { Component } from 'react';
import { Button, Card, Col, Form, Input, Row, Select, Switch, DatePicker } from 'antd';
import { connect } from 'dva';
import { Link } from 'dva/router'; // 引入 Link 组件

const { Option } = Select;

@connect(({ global, gm, loading, user }) => ({ // 1. 连接 user 状态
  collapsed: global.collapsed,
  submitting: loading.effects['gm/doCommand'] || loading.effects['gm/doCommandSchedule'],
  gm: gm,
  user: user, // <-- 获取用户信息
}))
@Form.create()
class GMForm extends Component {
  state = {
    command: {
      params: [],
      name: '加载中',
      desc: '加载中...',
    },
    selectedSeverIds: [],
    withScheduler: false, // 2. 增加 withScheduler 状态
    commandResult: null,
  };

  // 3. 修改 handleSubmit 以支持定时任务
  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { gm: { servers }, dispatch } = this.props;
        const selected_servers = this.state.command.requestServer ? [values.servers] : values.servers;
        const serverIds = selected_servers.map(serverId => {
          let s = servers.find(server => server.serverId === serverId);
          return s && s.id;
        });

        const commonPayload = {
          params: values.params || [],
          roleId: values.roleId,
          serverIds: serverIds,
        };

        if (this.state.withScheduler) {
          // 定时任务逻辑
          dispatch({
            type: 'gm/doCommandSchedule',
            payload: {
              data: {
                ...commonPayload,
                startTime: values.startTime.toDate().getTime(),
              },
            },
            commandName: this.state.command.name,
          }).then(this.handleSchedule);
        } else {
          // 立即执行逻辑
          dispatch({
            type: 'gm/doCommand',
            payload: {
              data: commonPayload,
            },
            commandName: this.state.command.name,
          }).then(this.handleCallBack);
        }

        this.setState({
          commandResult: null,
        });
      }
    });
  };

  // 立即执行的回调
  handleCallBack = data => {
    this.setState({
      commandResult: data === true ? '执行成功' : data,
    });
  };

  // 4. 新增定时任务的回调
  handleSchedule = (e) => {
    this.setState({
      commandResult: e === true ? "定时任务上传成功" : "定时任务上传失败",
    });
  };

  handleSelectAllServers = () => {
    const { gm: { servers }, form } = this.props;
    form.setFieldsValue({ servers: servers.map(server => server.serverId) });
  };
  
  // 5. 新增 Switch 的 onChange 事件处理
  ScheduleSwitchChange = (checked) => {
    this.setState({ withScheduler: checked });
  };

  componentDidMount() {
    this.props.dispatch({
      type: 'gm/fetchCommandById',
      commandId: this.props.match.params.commandId,
    }).then(command => this.setState({ command: command }));

    this.props.dispatch({
      type: 'gm/fetchServers',
    });
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.match.params.commandId && this.props.match.params.commandId !== nextProps.match.params.commandId) {
      this.props.dispatch({
        type: 'gm/fetchCommandById',
        commandId: nextProps.match.params.commandId,
      }).then(command => this.setState({ command: command, commandResult: null }));
    }
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { submitting, gm: { servers }, user: { currentUser } } = this.props;
    const { command, withScheduler } = this.state;

    const formItems = command.params.length > 0 ?
      command.params.map((p, index) => (
        <Form.Item key={index}>
          {getFieldDecorator('params.' + index, {
            rules: [{ required: true, message: '不能为空' }],
          })(
            <Input id={p.name} placeholder={p.desc} />,
          )}
        </Form.Item>
      )) : null;

    const roleItem = command.withRole ? (<Form.Item>
      {getFieldDecorator('roleId', {
        rules: [{ required: true, message: '角色Id不能为空' }],
      })(
        <Input id={'roleId'} placeholder={'角色Id'} />,
      )}
    </Form.Item>) : null;

    const commandResult = this.state.commandResult;
    // 返回结果是数组才排序
    const renderCommandResult = commandResult && Array.isArray(commandResult) ? commandResult.sort((a,b)=>a.serverId - b.serverId).map(a => (
      <Row gutter={16} key={a.serverId}>
        <Col span={6}>{a.serverId}</Col>
        <Col span={18}>
          <pre>{a.result}</pre>
        </Col>
      </Row>
    )) : commandResult; // 否则直接显示

    const serverItems = servers.sort((a,b)=>a.serverId - b.serverId).map((server, index) =>
      <Option key={index} value={server.serverId}>
        {server.name.length === 0 ? '服务器' + server.serverId : server.name + '(' + server.serverId + ')'}
      </Option>);
    
    const ServerSelect = command.requestServer ? (
      <Form.Item>
        {getFieldDecorator('servers', {
          rules: [{ required: true, message: '请选择服务器' }],
        })(
          <Select style={{ maxWidth: 286, width: '100%' }} allowClear={true}
                  placeholder="该命令需要指定服务器运行"
                  filterOption={(input, option) => option.props.value.toString().indexOf(input) === 0}
          >
            {serverItems}
          </Select>,
        )}
      </Form.Item>
    ) : (
      <Form.Item>
        {getFieldDecorator('servers', {
          rules: [{ required: true, message: '请选择服务器' }],
        })(
          <Select style={{ maxWidth: 1000, width: '100%' }} mode={'multiple'} allowClear={true}
                  placeholder="请选择服务器(可多选)"
                  filterOption={(input, option) => option.props.value.toString().indexOf(input) === 0}
          >
            {serverItems}
          </Select>,
        )}
        <Button onClick={this.handleSelectAllServers}>全选</Button>
      </Form.Item>
    );
    
    // 6. 增加管理员UI
    const adminScheduler = currentUser && currentUser.role && currentUser.role.roleName === 'Admin' ? (
      <div>
        定时任务 <Switch defaultChecked={withScheduler} onChange={this.ScheduleSwitchChange} />
        {withScheduler && (
          <Form.Item label="执行时间">
            {getFieldDecorator('startTime', {
              rules: [{ required: true, message: '请选择执行时间' }],
            })(
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择执行时间"
              />,
            )}
          </Form.Item>
        )}
      </div>
    ) : null;
    
    // 6. 增加管理员UI
    const adminBatchLink = currentUser && currentUser.role && currentUser.role.roleName === 'Admin' ? (
      <Link to={`/batch/${this.state.command.name}`}>批量处理</Link>
    ) : null;

    return (
      <>
        <Card title={command.desc + '(' + command.name + ')'} bordered={false} extra={adminBatchLink}>
          <Form onSubmit={this.handleSubmit}>
            {roleItem}
            {formItems}
            <Card bordered={false} bodyStyle={{ padding: 0 }}>
              {ServerSelect}
            </Card>
            
            {adminScheduler}

            <Form.Item>
              <Button type='primary' htmlType='submit' loading={submitting}>
                提交
              </Button>
            </Form.Item>
          </Form>
        </Card>
        <Card title="执行结果" bordered={false}>
          <div>
            {renderCommandResult}
          </div>
        </Card>
      </>
    );
  };
}

export default GMForm;