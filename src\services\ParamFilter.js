import request from '../utils/request';

export async function query() {
  return request('/api/paramFilters');
}

export async function createParamFilter(params) {
  return request('/paramFilters', {
    method: 'POST',
    body: params,
  });
}

export async function deleteParamFilter(paramFilterId) {
  return request('/api/paramFilters/' + paramFilterId, {
    method: 'DELETE',
  });
}

export async function findParamFilter(paramFilterId) {
  return request('/api/paramFilters/' + paramFilterId);
}

export async function addParamFilter(params) {
  return request('/roles/filters', {
    method: 'POST',
    body: params,
  });
}

