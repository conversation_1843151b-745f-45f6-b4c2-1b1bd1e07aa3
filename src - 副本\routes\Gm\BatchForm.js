import React, { Component } from 'react';
import { But<PERSON>, Card, List, Upload, Icon } from 'antd';
import { connect } from 'dva';
import { Form } from 'antd'; // 确保引入 Form

@connect(({ gm, loading }) => ({
  gm,
  submitting: loading.effects['gm/doCommandBatch'],
}))
@Form.create() // 虽然表单项是动态生成的，但整体结构仍可由 Form 管理
class BatchCommandForm extends Component {
  state = {
    fileList: [], // 用于存储待上传的文件
    commandResult: null, // 存储命令执行结果
    command: {
      params: [],
      name: '加载中',
      desc: '加载中...',
    },
  };

  componentDidMount() {
    const { dispatch, match } = this.props;
    // 组件加载时，根据 URL 中的 commandId 获取命令详情
    dispatch({
      type: 'gm/fetchCommandById',
      commandId: match.params.commandId,
    }).then(command => {
      if (command) {
        this.setState({ command });
      }
    });
  }

  // 如果路由变化，重新加载命令
  componentWillReceiveProps(nextProps) {
    if (
      nextProps.match.params.commandId &&
      this.props.match.params.commandId !== nextProps.match.params.commandId
    ) {
      this.props.dispatch({
        type: 'gm/fetchCommandById',
        commandId: nextProps.match.params.commandId,
      }).then(command => {
        if (command) {
          this.setState({ command, commandResult: null });
        }
      });
    }
  }

  // 文件上传前的钩子，返回 false 以便手动控制上传
  handleBeforeUpload = file => {
    this.setState({ fileList: [file] });
    return false;
  };
  
  // 上传文件并执行命令
  handleUpload = () => {
    const { fileList, command } = this.state;
    const { dispatch } = this.props;

    const file = fileList[0];
    if (!file) {
      return;
    }

    const formData = new FormData();
    formData.append('file', file);

    dispatch({
      type: 'gm/doCommandBatch',
      formData,
      commandName: command.name,
    }).then(this.handleCallBack);
  };

  // 处理命令执行结果
  handleCallBack = (data) => {
    this.setState({ commandResult: data });
  };

  // 清空显示的结果
  handleClearResult = () => {
    this.setState({ commandResult: null });
  };

  render() {
    const { command, fileList, commandResult } = this.state;
    
    // antd Upload 组件的属性
    const uploadProps = {
      onRemove: () => {
        this.setState({ fileList: [] });
      },
      beforeUpload: this.handleBeforeUpload,
      fileList,
    };
    
    // 根据命令参数动态生成列表，提示用户CSV格式
    const listDataSource = [
      'serverId (服务器ID, ;分割)',
      ...(command.withRole ? ['roleID (角色Id)'] : []),
      ...(command.params.map(p => ` ${p.name} (${p.desc})`)),
    ];
    
    return (
      <>
        <Card title={`${command.desc} (${command.name})`} bordered={false}>
          {/* 提示 CSV 文件格式 */}
          <List
            header={<div>CSV文件格式要求</div>}
            bordered
            dataSource={listDataSource}
            renderItem={item => (<List.Item>{item}</List.Item>)}
          />
          
          <div style={{marginTop: '16px'}}>CSV不需要表头</div>
          
          <div style={{marginTop: '16px'}}>
            <Upload {...uploadProps}>
              <Button>
                <Icon type="upload" /> 选择CSV文件
              </Button>
            </Upload>
            
            <Button
              type="primary"
              onClick={this.handleUpload}
              disabled={fileList.length === 0}
              style={{ marginTop: 16 }}
              loading={this.props.submitting}
            >
              提交执行
            </Button>
          </div>
        </Card>

        <Card
          title="执行结果"
          bordered={false}
          extra={<Icon type="close-circle-o" onClick={this.handleClearResult} />}
        >
          <div>
            <pre>{commandResult}</pre>
          </div>
        </Card>
      </>
    );
  }
}

export default BatchCommandForm;