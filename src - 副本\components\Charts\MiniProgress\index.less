@import "~antd/lib/style/themes/default.less";

.miniProgress {
  padding: 5px 0;
  position: relative;
  width: 100%;
  .progressWrap {
    background-color: @background-color-base;
    position: relative;
  }
  .progress {
    transition: all .4s cubic-bezier(.08, .82, .17, 1) 0s;
    border-radius: 1px 0 0 1px;
    background-color: @primary-color;
    width: 0;
    height: 100%;
  }
  .target {
    position: absolute;
    top: 0;
    bottom: 0;
    span {
      border-radius: 100px;
      position: absolute;
      top: 0;
      left: 0;
      height: 4px;
      width: 2px;
    }
    span:last-child {
      top: auto;
      bottom: 0;
    }
  }
}
