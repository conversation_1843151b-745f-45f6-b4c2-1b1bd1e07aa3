import { query, queryUsers, queryCurrent, queryMenu, createUser, deleteUser } from '../services/user';

export default {
  namespace: 'user',

  state: {
    list: [],
    currentUser: {},
    menu: [],
    users: [],
  },

  effects: {
    *fetch(_, { call, put }) {
      const response = yield call(query);
      yield put({
        type: 'save',
        payload: response,
      });
      return response;
    },
    *fetchCurrent(_, { call, put }) {
      const response = yield call(queryCurrent);
      if (response) {
        yield put({
          type: 'saveCurrentUser',
          payload: response,
        });
      }
    },
    *fetchMenu(_, { call, put }) {
      const response = yield call(queryMenu);
      if (response){
        yield put({
          type: 'saveMenu',
          payload: response,
        });
      }
    },
    *fetchUsers(_, { call, put }) {
      const response = yield call(queryUsers);
      const users = response._embedded.users.map(user=>{
        var s = user._links.self.href.split('/');
        user.id = s[s.length - 1];
        user.name = user.userName
        return user;
      });
      yield put({
        type: 'saveUsers',
        payload: response._embedded.users,
      });
      return response
    },
    *createUser({payload}, { call, put }) {
      yield call(createUser, payload);
      yield put({ type: 'fetchUsers', });
    },
    *deleteUser({userId}, { call, put }) {
      yield call(deleteUser, userId);
      yield put({ type: 'fetchUsers', });
    },

  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        list: action.payload,
      };
    },
    saveCurrentUser(state, action) {
      return {
        ...state,
        currentUser: action.payload,
      };
    },
    saveMenu(state, action) {
      return {
        ...state,
        menu: action.payload,
      };
    },
    saveUsers(state, action) {
      return {
        ...state,
        users: action.payload,
      };
    },
    changeNotifyCount(state, action) {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          notifyCount: action.payload,
        },
      };
    },
  },
};
